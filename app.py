from flask import Flask, render_template, request, send_file, jsonify
from utils.ocr import extract_text
from utils.translator import translate_text
from utils.image_processor import ImageTextProcessor
from PIL import Image, ImageDraw, ImageFont
import os
import logging
import traceback

app = Flask(__name__)
app.config['OUTPUT_FOLDER'] = 'output'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB 文件大小限制
os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 初始化图像处理器
image_processor = ImageTextProcessor()

# 文件大小和格式配置
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp', '.gif'}
MIN_IMAGE_SIZE = (10, 10)  # 最小图像尺寸
MAX_IMAGE_SIZE = (4000, 4000)  # 最大图像尺寸

@app.errorhandler(413)
def too_large(e):
    logger.warning("上传文件过大")
    return jsonify({"error": "文件大小超过限制 (最大16MB)"}), 413

def validate_file(file):
    """验证上传的文件"""
    # Flask 的 request.files.get() 在没有文件时返回 None
    # 但在有空的 input 时可能返回空的 FileStorage 对象
    if not file:
        return False, "没有上传文件"
    
    # 检查是否有文件名属性，以及文件名是否为空
    if not hasattr(file, 'filename'):
        return False, "无效的文件对象"
        
    if not file.filename or file.filename == '':
        return False, "没有选择文件"
    
    # 检查文件扩展名
    file_ext = os.path.splitext(file.filename.lower())[1]
    if file_ext not in ALLOWED_EXTENSIONS:
        return False, f"不支持的文件格式: {file_ext}。支持的格式: {', '.join(ALLOWED_EXTENSIONS)}"
    
    # 检查文件大小（通过读取内容长度）
    try:
        file.stream.seek(0, 2)  # 移动到文件末尾
        file_size = file.stream.tell()
        file.stream.seek(0)  # 重置到开头
        
        if file_size > MAX_FILE_SIZE:
            return False, f"文件大小 ({file_size / 1024 / 1024:.1f}MB) 超过限制 ({MAX_FILE_SIZE / 1024 / 1024}MB)"
        
        if file_size < 100:  # 小于100字节可能不是有效图片
            return False, "文件太小，可能不是有效的图片文件"
    except Exception as e:
        logger.error(f"文件大小检查失败: {str(e)}")
        return False, "文件读取失败"
    
    return True, "文件验证通过"

def load_font(size=20):
    """跨平台字体加载函数"""
    font_paths = [
        # Windows
        "C:/Windows/Fonts/arial.ttf",
        "C:/Windows/Fonts/simhei.ttf",
        "C:/Windows/Fonts/simsun.ttc",
        # macOS
        "/System/Library/Fonts/Arial.ttf",
        "/System/Library/Fonts/PingFang.ttc",
        # Linux
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
        # 相对路径
        "arial.ttf",
        "fonts/arial.ttf"
    ]
    
    for font_path in font_paths:
        try:
            if os.path.exists(font_path):
                return ImageFont.truetype(font_path, size=size)
        except Exception as e:
            logger.debug(f"尝试加载字体 {font_path} 失败: {str(e)}")
            continue
    
    # 如果所有字体都加载失败，使用默认字体
    logger.warning("所有字体加载失败，使用默认字体")
    return ImageFont.load_default()

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        logger.info("=== 收到POST请求 ===")
        try:
            logger.info("开始处理图片翻译请求")

            # 1. 获取参数
            file = request.files.get('image')
            target_language = request.form.get('target_language', 'ru')  # 默认俄语
            logger.info(f"接收到的文件对象: {file}, 类型: {type(file)}")
            logger.info(f"目标翻译语言: {target_language}")
            if file:
                logger.info(f"文件名: {getattr(file, 'filename', 'N/A')}")

            # 2. 文件验证
            is_valid, message = validate_file(file)
            logger.info(f"文件验证结果: {is_valid}, 消息: {message}")
            if not is_valid:
                logger.warning(f"文件验证失败: {message}")
                return jsonify({"error": message}), 400

            # 获取文件大小
            file.stream.seek(0, 2)  # 移动到文件末尾
            file_size = file.stream.tell()
            file.stream.seek(0)  # 重置到开头
            logger.info(f"接收到文件: {file.filename} (大小: {file_size / 1024:.1f}KB)")

            # 3. 图片加载和处理
            try:
                # 重置文件流指针
                file.stream.seek(0)
                image = Image.open(file.stream).convert("RGB")
                width, height = image.size
                logger.info(f"成功加载图片，尺寸: {width}x{height}")
                
                # 验证图像尺寸
                if width < MIN_IMAGE_SIZE[0] or height < MIN_IMAGE_SIZE[1]:
                    return jsonify({"error": f"图片尺寸过小，最小尺寸: {MIN_IMAGE_SIZE[0]}x{MIN_IMAGE_SIZE[1]}"}), 400
                
                if width > MAX_IMAGE_SIZE[0] or height > MAX_IMAGE_SIZE[1]:
                    logger.warning(f"图片尺寸较大: {width}x{height}，处理可能较慢")
                    
            except Exception as e:
                logger.error(f"图片加载失败: {str(e)}")
                return jsonify({"error": "图片文件损坏或格式不支持"}), 400

            # 4. OCR文字识别（使用详细样式信息）
            try:
                results = extract_text(image, use_detailed_ocr=True)
                logger.info(f"OCR识别完成，找到 {len(results)} 个文本区域")

                if not results:
                    logger.info("图片中未检测到文字")
                    return jsonify({"message": "图片中未检测到文字"}), 200

                # 记录识别到的文字样式信息
                for i, result in enumerate(results):
                    if isinstance(result, dict):
                        text = result.get('text', '')
                        style = result.get('style', {})
                        level = result.get('level', 'body')
                        logger.info(f"文字 {i}: {text[:20]}... 样式: 大小={style.get('font_size', 'N/A')} "
                                   f"加粗={style.get('is_bold', False)} 层级={level}")

            except Exception as e:
                logger.error(f"OCR识别失败: {str(e)}")
                return jsonify({"error": "文字识别失败"}), 500
            
            # 5. 字体加载
            try:
                font = load_font()
                logger.info("字体加载成功")
            except Exception as e:
                logger.error(f"字体加载失败: {str(e)}")
                return jsonify({"error": "字体加载失败"}), 500
            
            # 6. 文字翻译和图文替换
            translated_texts = []
            translated_count = 0

            # 翻译所有文本
            for i, result in enumerate(results):
                try:
                    # 获取文本内容
                    if isinstance(result, dict):
                        text = result.get('text', '')
                        text_level = result.get('level', 'body')
                    else:
                        # 兼容旧格式
                        text = result[1] if len(result) > 1 else ''
                        text_level = 'body'

                    if not text or not text.strip():
                        logger.info(f"跳过空文本区域 {i}")
                        translated_texts.append("")
                        continue

                    logger.info(f"翻译文本 {i} ({text_level}): {text[:50]}...")
                    translated = translate_text(text, target_lang=target_language)

                    if not translated:
                        logger.warning(f"翻译结果为空，使用原文: {text}")
                        translated = text

                    translated_texts.append(translated)
                    translated_count += 1
                    logger.info(f"翻译完成 {i}: {text[:20]}... -> {translated[:20]}...")

                except Exception as e:
                    logger.error(f"翻译文本区域 {i} 时出错: {str(e)}")
                    # 使用原文
                    original_text = result.get('text', '') if isinstance(result, dict) else (result[1] if len(result) > 1 else '')
                    translated_texts.append(original_text)
                    continue

            logger.info(f"成功翻译了 {translated_count} 个文本区域")

            # 使用图像处理器进行像素级精确图文替换
            try:
                logger.info("开始像素级精确图文替换处理")

                # 统一使用像素级精确处理
                image = image_processor.process_image_translation(
                    image, results, translated_texts, target_language
                )
                logger.info("像素级精确图文替换处理完成")

            except Exception as e:
                logger.error(f"图文替换处理失败: {str(e)}")
                # 如果图文替换失败，回退到简单的文字覆盖
                logger.info("回退到简单文字覆盖模式")
                draw = ImageDraw.Draw(image)
                for i, (result, translated) in enumerate(zip(results, translated_texts)):
                    if translated and translated.strip():
                        try:
                            # 获取位置信息
                            if isinstance(result, dict):
                                bbox = result['bbox']
                                x, y = bbox[0], bbox[1]
                            else:
                                box = result[0]
                                if isinstance(box[0], (list, tuple)) and len(box[0]) >= 2:
                                    x, y = int(box[0][0]), int(box[0][1])
                                else:
                                    continue

                            draw.text((x, y), translated, fill="red", font=font)
                        except Exception as draw_error:
                            logger.error(f"简单绘制失败 {i}: {str(draw_error)}")
                            continue
            
            # 7. 保存结果图片
            try:
                output_path = os.path.join(app.config['OUTPUT_FOLDER'], 'translated_image.png')
                image.save(output_path)
                logger.info(f"图片保存成功: {output_path}")
                return send_file(output_path, mimetype='image/png')
            except Exception as e:
                logger.error(f"图片保存失败: {str(e)}")
                return jsonify({"error": "图片保存失败"}), 500
                
        except Exception as e:
            logger.error(f"处理请求时发生未预期的错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return jsonify({"error": "服务器内部错误"}), 500
    
    return render_template('index.html')

if __name__ == '__main__':
    app.run(debug=True)
