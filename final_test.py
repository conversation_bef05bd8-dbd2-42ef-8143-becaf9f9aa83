#!/usr/bin/env python3
"""
最终测试脚本：验证所有修复的功能
"""

import requests
import json
from PIL import Image, ImageDraw, ImageFont
import io

BASE_URL = "http://localhost:5000"

def create_test_image_with_text():
    """创建包含文字的测试图片"""
    image = Image.new('RGB', (400, 200), (255, 255, 255))
    draw = ImageDraw.Draw(image)
    
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    draw.text((50, 50), "Hello World", fill=(0, 0, 0), font=font)
    draw.text((50, 100), "Test Image", fill=(0, 0, 0), font=font)
    
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    return img_byte_arr

def test_case(name, test_func):
    """测试用例包装器"""
    print(f"\n{'='*50}")
    print(f"测试: {name}")
    print('='*50)
    try:
        result = test_func()
        if result:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
        return result
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_get_request():
    """测试GET请求"""
    response = requests.get(f"{BASE_URL}/")
    print(f"状态码: {response.status_code}")
    print(f"内容类型: {response.headers.get('content-type')}")
    return response.status_code == 200 and 'text/html' in response.headers.get('content-type', '')

def test_post_no_file():
    """测试POST请求无文件 - 应该返回400"""
    response = requests.post(f"{BASE_URL}/")
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 400:
        try:
            data = response.json()
            return "error" in data and "没有上传文件" in data["error"]
        except:
            return False
    return False

def test_post_invalid_format():
    """测试POST请求无效文件格式 - 应该返回400"""
    files = {'image': ('test.txt', 'This is not an image', 'text/plain')}
    response = requests.post(f"{BASE_URL}/", files=files)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 400:
        try:
            data = response.json()
            return "error" in data and "不支持的文件格式" in data["error"]
        except:
            return False
    return False

def test_post_empty_file():
    """测试POST请求空文件 - 应该返回400"""
    files = {'image': ('empty.png', b'', 'image/png')}
    response = requests.post(f"{BASE_URL}/", files=files)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 400:
        try:
            data = response.json()
            return "error" in data and ("文件太小" in data["error"] or "没有选择文件" in data["error"])
        except:
            return False
    return False

def test_post_small_image():
    """测试POST请求小图片 - 应该返回400"""
    # 创建一个5x5的小图片
    image = Image.new('RGB', (5, 5), (255, 255, 255))
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)

    files = {'image': ('small.png', img_byte_arr, 'image/png')}
    response = requests.post(f"{BASE_URL}/", files=files)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")

    if response.status_code == 400:
        try:
            data = response.json()
            # 接受两种可能的错误信息
            return "error" in data and ("图片尺寸过小" in data["error"] or "文件太小" in data["error"])
        except:
            return False
    return False

def test_post_blank_image():
    """测试POST请求空白图片 - 应该返回200但提示无文字"""
    # 创建一个空白图片
    image = Image.new('RGB', (200, 200), (255, 255, 255))
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    
    files = {'image': ('blank.png', img_byte_arr, 'image/png')}
    response = requests.post(f"{BASE_URL}/", files=files)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.text}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            return "message" in data and "未检测到文字" in data["message"]
        except:
            return False
    return False

def test_post_image_with_text():
    """测试POST请求包含文字的图片 - 应该返回翻译后的图片"""
    img_byte_arr = create_test_image_with_text()
    
    files = {'image': ('text_image.png', img_byte_arr, 'image/png')}
    response = requests.post(f"{BASE_URL}/", files=files)
    print(f"状态码: {response.status_code}")
    print(f"内容类型: {response.headers.get('content-type', 'N/A')}")
    print(f"内容长度: {len(response.content)} 字节")
    
    if response.status_code == 200 and 'image/png' in response.headers.get('content-type', ''):
        # 保存返回的图片
        with open('final_test_output.png', 'wb') as f:
            f.write(response.content)
        print("✅ 成功保存翻译后的图片: final_test_output.png")
        return True
    return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行完整测试套件...")
    
    tests = [
        ("GET请求", test_get_request),
        ("POST无文件", test_post_no_file),
        ("POST无效格式", test_post_invalid_format),
        ("POST空文件", test_post_empty_file),
        ("POST小图片", test_post_small_image),
        ("POST空白图片", test_post_blank_image),
        ("POST文字图片", test_post_image_with_text),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        if test_case(name, test_func):
            passed += 1
    
    print(f"\n{'='*60}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有测试通过！应用程序修复成功！")
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，需要进一步检查。")
    
    return passed == total

if __name__ == "__main__":
    print("请确保Flask应用正在运行 (python app.py)")
    print("然后按Enter键开始测试...")
    input()
    
    success = run_all_tests()
    
    if success:
        print("\n🎯 修复总结:")
        print("✅ 统一POST无文件/空字段的状态码为400")
        print("✅ 处理无效文件格式时返回友好提示（非500）")
        print("✅ 所有测试用例通过，包括GET、POST各场景")
        print("✅ 详细的日志记录帮助调试")
        print("✅ 完善的异常处理机制")
    
    print("\n测试完成！")
