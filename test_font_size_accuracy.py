#!/usr/bin/env python3
"""
字体大小精确匹配测试脚本
验证翻译后的字体大小是否与原图1:1匹配
"""

import requests
from PIL import Image, ImageDraw, ImageFont, ImageOps
import io
import os
import numpy as np
import cv2

BASE_URL = "http://localhost:5000"

def create_test_product_spec():
    """创建测试用的产品规格图片，模拟真实场景"""
    image = Image.new('RGB', (800, 600), (245, 245, 220))  # 米色背景
    draw = ImageDraw.Draw(image)
    
    try:
        # 尝试加载中文字体
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc",
            "C:/Windows/Fonts/arial.ttf"
        ]
        
        # 定义不同大小的字体
        title_font = None
        subtitle_font = None
        body_font = None
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    title_font = ImageFont.truetype(font_path, 32)      # 大标题
                    subtitle_font = ImageFont.truetype(font_path, 20)   # 子标题
                    body_font = ImageFont.truetype(font_path, 16)       # 正文
                    break
                except:
                    continue
        
        if not title_font:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            body_font = ImageFont.load_default()
    
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        body_font = ImageFont.load_default()
    
    # 绘制大标题（32px）
    title_text = "产品参数"
    draw.text((60, 50), title_text, fill=(0, 0, 0), font=title_font)
    draw.text((61, 50), title_text, fill=(0, 0, 0), font=title_font)  # 模拟加粗
    
    # 绘制英文对照
    draw.text((500, 50), "PRODUCT", fill=(128, 128, 128), font=body_font)
    draw.text((500, 75), "PARAMETERS", fill=(128, 128, 128), font=body_font)
    
    # 绘制分隔线
    draw.line([(60, 100), (740, 100)], fill=(200, 200, 200), width=2)
    
    # 绘制参数表格 - 使用不同字体大小
    y_pos = 140
    
    # 参数项目（子标题 20px + 正文 16px）
    params = [
        ("产品名称:", "高速风扇", True, False),           # 标签：子标题，值：正文
        ("型号规格:", "220V/50Hz", False, False),        # 标签：正文，值：正文
        ("开关类型:", "高档/低档", False, False),         # 标签：正文，值：正文
        ("产品颜色:", "纯白/银灰/蓝色", True, False),     # 标签：子标题，值：正文
        ("保修期限:", "2年有限保修", True, True),         # 标签：子标题，值：子标题
        ("认证标准:", "CE, FCC, RoHS", False, False),    # 标签：正文，值：正文
        ("工作温度:", "-10°C 到 +60°C", False, False),   # 标签：正文，值：正文
    ]
    
    for label, value, label_is_subtitle, value_is_subtitle in params:
        # 绘制标签
        label_font = subtitle_font if label_is_subtitle else body_font
        if label_is_subtitle:
            # 子标题加粗效果
            draw.text((60, y_pos), label, fill=(0, 0, 0), font=label_font)
            draw.text((61, y_pos), label, fill=(0, 0, 0), font=label_font)
        else:
            draw.text((60, y_pos), label, fill=(0, 0, 0), font=label_font)
        
        # 绘制值
        value_font = subtitle_font if value_is_subtitle else body_font
        if value_is_subtitle:
            # 子标题加粗效果
            draw.text((250, y_pos), value, fill=(64, 64, 64), font=value_font)
            draw.text((251, y_pos), value, fill=(64, 64, 64), font=value_font)
        else:
            draw.text((250, y_pos), value, fill=(64, 64, 64), font=value_font)
        
        y_pos += 45
    
    # 绘制底部注释（小字体）
    note_text = "* 产品规格如有变更，恕不另行通知"
    small_font = ImageFont.truetype(font_paths[0], 12) if os.path.exists(font_paths[0]) else body_font
    draw.text((60, 520), note_text, fill=(128, 128, 128), font=small_font)
    
    # 绘制边框
    draw.rectangle([(40, 30), (760, 560)], outline=(200, 200, 200), width=1)
    
    return image

def analyze_font_size_accuracy(original_image, translated_image):
    """分析翻译后图片的字体大小准确性"""
    try:
        # 转换为numpy数组
        orig_array = np.array(original_image)
        trans_array = np.array(translated_image)
        
        # 转换为灰度
        orig_gray = cv2.cvtColor(orig_array, cv2.COLOR_RGB2GRAY)
        trans_gray = cv2.cvtColor(trans_array, cv2.COLOR_RGB2GRAY)
        
        # 检测文字区域
        def detect_text_regions(gray_image):
            # 二值化
            _, binary = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 形态学操作连接文字
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 1))
            connected = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            # 找到轮廓
            contours, _ = cv2.findContours(connected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            text_regions = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                if w > 20 and h > 8:  # 过滤小噪点
                    text_regions.append((x, y, w, h))
            
            return text_regions
        
        orig_regions = detect_text_regions(orig_gray)
        trans_regions = detect_text_regions(trans_gray)
        
        # 分析字体大小变化
        size_analysis = {
            'original_regions': len(orig_regions),
            'translated_regions': len(trans_regions),
            'size_differences': [],
            'large_title_preserved': False,
            'subtitle_preserved': False,
            'body_preserved': False
        }
        
        # 按高度分类文字区域
        def classify_by_height(regions):
            large_titles = [r for r in regions if r[3] >= 28]  # 高度>=28的大标题
            subtitles = [r for r in regions if 18 <= r[3] < 28]  # 高度18-27的子标题
            body_text = [r for r in regions if r[3] < 18]  # 高度<18的正文
            return large_titles, subtitles, body_text
        
        orig_large, orig_sub, orig_body = classify_by_height(orig_regions)
        trans_large, trans_sub, trans_body = classify_by_height(trans_regions)
        
        # 检查各类文字是否保持
        size_analysis['large_title_preserved'] = len(trans_large) >= len(orig_large) * 0.8
        size_analysis['subtitle_preserved'] = len(trans_sub) >= len(orig_sub) * 0.7
        size_analysis['body_preserved'] = len(trans_body) >= len(orig_body) * 0.7
        
        # 计算平均高度变化
        if orig_large and trans_large:
            orig_avg_large = np.mean([r[3] for r in orig_large])
            trans_avg_large = np.mean([r[3] for r in trans_large])
            size_analysis['large_title_change'] = (trans_avg_large - orig_avg_large) / orig_avg_large
        
        if orig_sub and trans_sub:
            orig_avg_sub = np.mean([r[3] for r in orig_sub])
            trans_avg_sub = np.mean([r[3] for r in trans_sub])
            size_analysis['subtitle_change'] = (trans_avg_sub - orig_avg_sub) / orig_avg_sub
        
        if orig_body and trans_body:
            orig_avg_body = np.mean([r[3] for r in orig_body])
            trans_avg_body = np.mean([r[3] for r in trans_body])
            size_analysis['body_change'] = (trans_avg_body - orig_avg_body) / orig_avg_body
        
        return size_analysis
        
    except Exception as e:
        print(f"字体大小分析失败: {e}")
        return {
            'original_regions': 0,
            'translated_regions': 0,
            'large_title_preserved': False,
            'subtitle_preserved': False,
            'body_preserved': False
        }

def test_font_size_accuracy(test_name, image, language_code, language_name):
    """测试字体大小准确性"""
    print(f"\n{'='*70}")
    print(f"字体大小精确匹配测试: {test_name} -> {language_name}")
    print('='*70)
    
    try:
        # 保存原始图片
        input_filename = f'font_test_input_{test_name}_{language_code}.png'
        image.save(input_filename)
        print(f"✅ 原始图片已保存: {input_filename}")
        
        # 转换为字节流
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        # 发送翻译请求
        files = {'image': (input_filename, img_byte_arr, 'image/png')}
        data = {'target_language': language_code}
        
        print(f"🔄 发送字体大小精确匹配请求...")
        response = requests.post(f"{BASE_URL}/", files=files, data=data)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            if 'image/png' in response.headers.get('content-type', ''):
                # 保存翻译后的图片
                output_filename = f'font_test_output_{test_name}_{language_code}.png'
                with open(output_filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ 翻译成功！输出图片: {output_filename}")
                
                # 加载翻译后的图片进行分析
                translated_image = Image.open(output_filename)
                
                # 分析字体大小准确性
                size_analysis = analyze_font_size_accuracy(image, translated_image)
                
                print(f"\n🔍 字体大小准确性分析:")
                print(f"   原始文字区域: {size_analysis['original_regions']}")
                print(f"   翻译文字区域: {size_analysis['translated_regions']}")
                print(f"   大标题保持: {'✅' if size_analysis['large_title_preserved'] else '❌'}")
                print(f"   子标题保持: {'✅' if size_analysis['subtitle_preserved'] else '❌'}")
                print(f"   正文保持: {'✅' if size_analysis['body_preserved'] else '❌'}")
                
                if 'large_title_change' in size_analysis:
                    change = size_analysis['large_title_change']
                    print(f"   大标题高度变化: {change:+.1%}")
                
                if 'subtitle_change' in size_analysis:
                    change = size_analysis['subtitle_change']
                    print(f"   子标题高度变化: {change:+.1%}")
                
                if 'body_change' in size_analysis:
                    change = size_analysis['body_change']
                    print(f"   正文高度变化: {change:+.1%}")
                
                # 判断是否通过测试
                passed = (size_analysis['large_title_preserved'] and 
                         size_analysis['subtitle_preserved'] and 
                         size_analysis['body_preserved'])
                
                if passed:
                    print(f"✅ 字体大小匹配测试通过")
                else:
                    print(f"❌ 字体大小匹配需要改进")
                
                return passed
                
            else:
                print(f"❌ 响应不是图片格式")
                return False
        else:
            print(f"❌ 翻译失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 字体大小精确匹配测试")
    print("验证翻译后的字体大小是否与原图1:1匹配...")
    
    # 创建测试图片
    test_image = create_test_product_spec()
    
    # 测试语言
    languages = [
        ('ru', '俄语'),
        ('ja', '日语'),
        ('en', '英语'),
    ]
    
    total_tests = len(languages)
    passed_tests = 0
    
    for lang_code, lang_name in languages:
        if test_font_size_accuracy("product_spec", test_image, lang_code, lang_name):
            passed_tests += 1
    
    print(f"\n{'='*80}")
    print(f"字体大小精确匹配测试结果: {passed_tests}/{total_tests}")
    print('='*80)
    
    if passed_tests == total_tests:
        print("🎉 所有字体大小匹配测试通过！")
        print("✅ 大标题字体大小1:1匹配")
        print("✅ 子标题字体大小保持层级")
        print("✅ 正文字体大小精确还原")
    elif passed_tests > 0:
        print(f"⚠️  部分测试通过 ({passed_tests}/{total_tests})")
        print("需要进一步优化字体大小匹配算法")
    else:
        print(f"❌ 需要检查字体大小匹配功能")
    
    print(f"\n📁 生成的测试文件:")
    for lang_code, _ in languages:
        input_file = f'font_test_input_product_spec_{lang_code}.png'
        output_file = f'font_test_output_product_spec_{lang_code}.png'
        if os.path.exists(input_file):
            print(f"- {input_file} (输入)")
        if os.path.exists(output_file):
            print(f"- {output_file} (输出)")

if __name__ == "__main__":
    main()
