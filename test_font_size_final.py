#!/usr/bin/env python3
"""
最终字体大小对比测试
专门测试优化后的字体大小效果
"""

import requests
from PIL import Image, ImageDraw, ImageFont
import io
import os

BASE_URL = "http://localhost:5000"

def create_product_spec_test():
    """创建与用户提供图片相似的产品规格测试图"""
    image = Image.new('RGB', (800, 600), (245, 245, 220))  # 米色背景
    draw = ImageDraw.Draw(image)
    
    try:
        # 尝试加载中文字体
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc",
            "C:/Windows/Fonts/arial.ttf"
        ]
        
        # 定义字体 - 模拟真实产品规格表的字体大小
        title_font = None
        subtitle_font = None
        body_font = None
        number_font = None
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    title_font = ImageFont.truetype(font_path, 42)      # 超大标题 42px
                    subtitle_font = ImageFont.truetype(font_path, 24)   # 子标题 24px  
                    body_font = ImageFont.truetype(font_path, 20)       # 正文 20px
                    number_font = ImageFont.truetype(font_path, 32)     # 重要数字 32px
                    break
                except:
                    continue
        
        if not title_font:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            body_font = ImageFont.load_default()
            number_font = ImageFont.load_default()
    
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        body_font = ImageFont.load_default()
        number_font = ImageFont.load_default()
    
    # 绘制右上角英文标识
    draw.text((580, 50), "PRODUCT", fill=(128, 128, 128), font=body_font)
    draw.text((580, 75), "PARAMETERS", fill=(128, 128, 128), font=body_font)
    
    # 绘制超大标题（42px，加粗效果）
    title_text = "产品参数"
    draw.text((70, 50), title_text, fill=(0, 0, 0), font=title_font)
    draw.text((71, 50), title_text, fill=(0, 0, 0), font=title_font)  # 模拟加粗
    draw.text((70, 51), title_text, fill=(0, 0, 0), font=title_font)  # 模拟加粗
    draw.text((71, 51), title_text, fill=(0, 0, 0), font=title_font)  # 模拟加粗
    
    # 绘制分隔线
    draw.line([(70, 110), (730, 110)], fill=(200, 200, 200), width=2)
    
    # 绘制参数表格
    y_pos = 150
    
    # 参数项目
    params = [
        ("产品名称:", "高转速吹风机", True, False),
        ("产品推荐:", "发廊/理发店/家用", True, False),
        ("额定电压:", "220V", True, True),  # 数字重要
        ("额定频率:", "50Hz", True, True),  # 数字重要
        ("开关档位:", "6档调节", True, False),
        ("产品功能:", "速干/蓝光/恒温", True, False),
        ("产品颜色:", "如下图所示", True, False),
        ("产品电线:", "加粗加长电源线", True, False),
    ]
    
    for label, value, is_important, is_number in params:
        # 绘制标签（子标题字体）
        if is_important:
            # 重要项目使用子标题字体并加粗
            draw.text((70, y_pos), label, fill=(0, 0, 0), font=subtitle_font)
            draw.text((71, y_pos), label, fill=(0, 0, 0), font=subtitle_font)  # 加粗效果
        else:
            draw.text((70, y_pos), label, fill=(0, 0, 0), font=body_font)
        
        # 绘制值
        if is_number:
            # 重要数字使用大字体并加粗
            draw.text((300, y_pos-5), value, fill=(0, 0, 0), font=number_font)
            draw.text((301, y_pos-5), value, fill=(0, 0, 0), font=number_font)  # 加粗效果
            draw.text((300, y_pos-4), value, fill=(0, 0, 0), font=number_font)  # 加粗效果
        else:
            # 正常值使用正文字体
            draw.text((300, y_pos), value, fill=(64, 64, 64), font=body_font)
        
        y_pos += 50
    
    # 绘制边框
    draw.rectangle([(50, 30), (750, 570)], outline=(200, 200, 200), width=1)
    
    return image

def test_final_comparison():
    """测试最终优化效果"""
    print(f"\n{'='*80}")
    print(f"🎯 最终字体大小对比测试")
    print(f"测试优化后的字体大小是否与原图匹配")
    print('='*80)
    
    try:
        # 创建测试图片
        test_image = create_product_spec_test()
        
        # 保存原始图片
        input_filename = 'final_test_input.png'
        test_image.save(input_filename)
        print(f"✅ 原始图片已保存: {input_filename}")
        
        # 转换为字节流
        img_byte_arr = io.BytesIO()
        test_image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        # 测试俄语翻译
        files = {'image': (input_filename, img_byte_arr, 'image/png')}
        data = {'target_language': 'ru'}
        
        print(f"🔄 发送俄语翻译请求...")
        response = requests.post(f"{BASE_URL}/", files=files, data=data)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            if 'image/png' in response.headers.get('content-type', ''):
                # 保存翻译后的图片
                output_filename = 'final_test_output_ru.png'
                with open(output_filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ 翻译成功！输出图片: {output_filename}")
                print(f"📁 请对比查看:")
                print(f"   原图: {input_filename}")
                print(f"   译图: {output_filename}")
                
                # 分析结果
                print(f"\n📋 对比要点:")
                print(f"1. 标题 '产品参数' → 'Параметры продукта' 字体大小是否匹配")
                print(f"2. 参数名称字体是否足够大且加粗")
                print(f"3. 重要数字 '220V', '50Hz' 是否突出显示")
                print(f"4. 整体字体层级是否清晰")
                
                return True
                
            else:
                print(f"❌ 响应不是图片格式")
                return False
        else:
            print(f"❌ 翻译失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 最终字体大小优化测试")
    print("验证字体大小是否达到1:1匹配效果...")
    
    success = test_final_comparison()
    
    if success:
        print(f"\n🎉 测试完成！请查看生成的对比图片")
        print(f"📝 如果字体大小仍然偏小，我们将继续调整倍数")
    else:
        print(f"\n❌ 测试失败，请检查应用程序状态")

if __name__ == "__main__":
    main()
