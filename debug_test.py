#!/usr/bin/env python3
"""
调试测试脚本
"""

import requests

BASE_URL = "http://localhost:5000"

def test_post_with_debug():
    """测试POST请求并打印详细信息"""
    print("测试POST请求（无文件）...")
    
    # 测试1：完全空的POST请求
    print("\n1. 完全空的POST请求:")
    try:
        response = requests.post(f"{BASE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"异常: {e}")
    
    # 测试2：带空的files参数
    print("\n2. 带空的files参数:")
    try:
        response = requests.post(f"{BASE_URL}/", files={})
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"异常: {e}")
    
    # 测试3：带空的image字段
    print("\n3. 带空的image字段:")
    try:
        response = requests.post(f"{BASE_URL}/", files={'image': ''})
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text[:200]}...")
    except Exception as e:
        print(f"异常: {e}")

if __name__ == "__main__":
    test_post_with_debug()
