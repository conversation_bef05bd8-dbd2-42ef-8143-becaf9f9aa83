#!/usr/bin/env python3
"""
像素级精确测试脚本 - 验证文字残留和样式匹配问题修复
"""

import requests
from PIL import Image, ImageDraw, ImageFont
import io
import os
import numpy as np
import cv2

BASE_URL = "http://localhost:5000"

def create_chinese_product_spec():
    """创建中文产品规格图片，模拟真实场景"""
    image = Image.new('RGB', (800, 600), (245, 245, 220))  # 米色背景
    draw = ImageDraw.Draw(image)
    
    try:
        # 尝试加载中文字体
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc",
            "C:/Windows/Fonts/arial.ttf"
        ]
        
        title_font = None
        body_font = None
        small_font = None
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    title_font = ImageFont.truetype(font_path, 28)
                    body_font = ImageFont.truetype(font_path, 16)
                    small_font = ImageFont.truetype(font_path, 12)
                    break
                except:
                    continue
        
        if not title_font:
            title_font = ImageFont.load_default()
            body_font = ImageFont.load_default()
            small_font = ImageFont.load_default()
    
    except:
        title_font = ImageFont.load_default()
        body_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 绘制标题（加粗效果）
    title_text = "产品参数"
    draw.text((50, 30), title_text, fill=(0, 0, 0), font=title_font)
    draw.text((51, 30), title_text, fill=(0, 0, 0), font=title_font)  # 模拟加粗
    
    # 绘制分隔线
    draw.line([(50, 80), (750, 80)], fill=(128, 128, 128), width=2)
    
    # 绘制参数表格
    y_pos = 120
    
    # 中文参数项目
    params = [
        ("产品名称:", "高速风扇", True),
        ("型号规格:", "220V/50Hz", False),
        ("开关类型:", "高档/低档", False),
        ("产品颜色:", "纯白/银灰/蓝色", False),
        ("保修期限:", "2年有限保修", True),
        ("认证标准:", "CE, FCC, RoHS", False),
        ("工作温度:", "-10°C 到 +60°C", False),
    ]
    
    for label, value, is_bold in params:
        # 绘制标签
        if is_bold:
            # 模拟加粗
            draw.text((50, y_pos), label, fill=(0, 0, 0), font=body_font)
            draw.text((51, y_pos), label, fill=(0, 0, 0), font=body_font)
        else:
            draw.text((50, y_pos), label, fill=(0, 0, 0), font=body_font)
        
        # 绘制值
        draw.text((200, y_pos), value, fill=(64, 64, 64), font=body_font)
        
        y_pos += 40
    
    # 绘制底部注释
    note_text = "* 产品规格如有变更，恕不另行通知"
    draw.text((50, 520), note_text, fill=(128, 128, 128), font=small_font)
    
    # 绘制边框
    draw.rectangle([(30, 20), (770, 560)], outline=(200, 200, 200), width=1)
    
    return image

def analyze_text_residue(original_image, translated_image):
    """分析翻译后图片是否有原文字残留"""
    try:
        # 转换为numpy数组
        orig_array = np.array(original_image)
        trans_array = np.array(translated_image)
        
        # 计算差异
        diff = cv2.absdiff(orig_array, trans_array)
        
        # 转换为灰度
        if len(diff.shape) == 3:
            diff_gray = cv2.cvtColor(diff, cv2.COLOR_RGB2GRAY)
        else:
            diff_gray = diff
        
        # 计算变化区域
        _, thresh = cv2.threshold(diff_gray, 30, 255, cv2.THRESH_BINARY)
        
        # 计算变化像素比例
        changed_pixels = np.sum(thresh > 0)
        total_pixels = thresh.size
        change_ratio = changed_pixels / total_pixels
        
        # 分析是否有文字残留（通过检测小的未变化区域）
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        small_unchanged_areas = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            if 50 < area < 500:  # 可能是残留文字的大小
                small_unchanged_areas += 1
        
        return {
            'change_ratio': change_ratio,
            'changed_pixels': changed_pixels,
            'potential_residue_areas': small_unchanged_areas,
            'has_residue': small_unchanged_areas > 5  # 如果有超过5个小区域，可能有残留
        }
        
    except Exception as e:
        print(f"残留分析失败: {e}")
        return {
            'change_ratio': 0,
            'changed_pixels': 0,
            'potential_residue_areas': 0,
            'has_residue': False
        }

def test_pixel_perfect_translation(test_name, image, language_code, language_name):
    """测试像素级精确翻译"""
    print(f"\n{'='*70}")
    print(f"像素级精确测试: {test_name} -> {language_name}")
    print('='*70)
    
    try:
        # 保存原始图片
        input_filename = f'pixel_test_input_{test_name}_{language_code}.png'
        image.save(input_filename)
        print(f"✅ 原始图片已保存: {input_filename}")
        
        # 转换为字节流
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        # 发送翻译请求
        files = {'image': (input_filename, img_byte_arr, 'image/png')}
        data = {'target_language': language_code}
        
        print(f"🔄 发送像素级精确翻译请求...")
        response = requests.post(f"{BASE_URL}/", files=files, data=data)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            if 'image/png' in response.headers.get('content-type', ''):
                # 保存翻译后的图片
                output_filename = f'pixel_test_output_{test_name}_{language_code}.png'
                with open(output_filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ 翻译成功！输出图片: {output_filename}")
                print(f"📊 文件大小: {len(response.content)} 字节")
                
                # 加载翻译后的图片进行分析
                translated_image = Image.open(output_filename)
                
                # 分析文字残留
                residue_analysis = analyze_text_residue(image, translated_image)
                
                print(f"\n🔍 文字残留分析:")
                print(f"   变化像素比例: {residue_analysis['change_ratio']:.3f}")
                print(f"   变化像素数量: {residue_analysis['changed_pixels']}")
                print(f"   可疑残留区域: {residue_analysis['potential_residue_areas']}")
                
                if residue_analysis['has_residue']:
                    print(f"⚠️  检测到可能的文字残留")
                    return False
                else:
                    print(f"✅ 未检测到文字残留")
                
                # 检查图片尺寸
                if image.size == translated_image.size:
                    print(f"✅ 图片尺寸保持一致: {image.size}")
                else:
                    print(f"⚠️  图片尺寸发生变化: {image.size} -> {translated_image.size}")
                
                return True
                
            else:
                print(f"❌ 响应不是图片格式")
                return False
        else:
            print(f"❌ 翻译失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 像素级精确翻译测试")
    print("验证文字残留和样式匹配问题修复...")
    
    # 创建测试图片
    chinese_spec = create_chinese_product_spec()
    
    # 测试语言
    languages = [
        ('ru', '俄语'),
        ('ja', '日语'),
        ('en', '英语'),
    ]
    
    total_tests = len(languages)
    passed_tests = 0
    
    for lang_code, lang_name in languages:
        if test_pixel_perfect_translation("chinese_spec", chinese_spec, lang_code, lang_name):
            passed_tests += 1
    
    print(f"\n{'='*80}")
    print(f"像素级精确翻译测试结果: {passed_tests}/{total_tests}")
    print('='*80)
    
    if passed_tests == total_tests:
        print("🎉 所有像素级精确测试通过！")
        print("✅ 文字残留问题已解决")
        print("✅ 样式匹配功能正常")
        print("✅ inpaint算法工作正常")
    elif passed_tests > 0:
        print(f"⚠️  部分测试通过 ({passed_tests}/{total_tests})")
        print("可能需要进一步优化某些语言的处理")
    else:
        print(f"❌ 需要检查像素级精确处理功能")
    
    print(f"\n📁 生成的测试文件:")
    for lang_code, _ in languages:
        input_file = f'pixel_test_input_chinese_spec_{lang_code}.png'
        output_file = f'pixel_test_output_chinese_spec_{lang_code}.png'
        if os.path.exists(input_file):
            print(f"- {input_file} (输入)")
        if os.path.exists(output_file):
            print(f"- {output_file} (输出)")

if __name__ == "__main__":
    main()
