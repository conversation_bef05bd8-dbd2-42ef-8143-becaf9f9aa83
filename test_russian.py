#!/usr/bin/env python3
"""
俄语翻译功能快速测试
"""

import requests
from PIL import Image, ImageDraw, ImageFont
import io
import os

BASE_URL = "http://localhost:5000"

def create_simple_chinese_image():
    """创建简单的中文测试图片"""
    image = Image.new('RGB', (400, 200), (255, 255, 255))
    draw = ImageDraw.Draw(image)
    
    # 使用默认字体绘制中文
    try:
        font = ImageFont.load_default()
        draw.text((50, 50), "你好世界", fill=(0, 0, 0), font=font)
        draw.text((50, 100), "测试图片", fill=(0, 0, 0), font=font)
    except:
        # 如果字体加载失败，使用英文
        draw.text((50, 50), "Hello World", fill=(0, 0, 0))
        draw.text((50, 100), "Test Image", fill=(0, 0, 0))
    
    return image

def test_russian_translation():
    """测试俄语翻译功能"""
    print("🔧 测试俄语翻译功能...")
    
    try:
        # 创建测试图片
        image = create_simple_chinese_image()
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        # 发送俄语翻译请求
        files = {'image': ('test_chinese.png', img_byte_arr, 'image/png')}
        data = {'target_language': 'ru'}  # 俄语
        
        print("发送俄语翻译请求...")
        response = requests.post(f"{BASE_URL}/", files=files, data=data)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            if 'image/png' in response.headers.get('content-type', ''):
                # 保存翻译后的图片
                with open('russian_translation_test.png', 'wb') as f:
                    f.write(response.content)
                print("✅ 俄语翻译成功！")
                print(f"✅ 保存为: russian_translation_test.png")
                print(f"✅ 文件大小: {len(response.content)} 字节")
                return True
            else:
                print("✅ 响应成功，但可能是无文字检测")
                print(f"响应内容: {response.text}")
                return True
        else:
            print("❌ 俄语翻译失败")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_english_translation():
    """测试英语翻译功能"""
    print("\n🔧 测试英语翻译功能...")
    
    try:
        # 创建测试图片
        image = create_simple_chinese_image()
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        # 发送英语翻译请求
        files = {'image': ('test_chinese.png', img_byte_arr, 'image/png')}
        data = {'target_language': 'en'}  # 英语
        
        print("发送英语翻译请求...")
        response = requests.post(f"{BASE_URL}/", files=files, data=data)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            if 'image/png' in response.headers.get('content-type', ''):
                # 保存翻译后的图片
                with open('english_translation_test.png', 'wb') as f:
                    f.write(response.content)
                print("✅ 英语翻译成功！")
                print(f"✅ 保存为: english_translation_test.png")
                return True
            else:
                print("✅ 响应成功")
                return True
        else:
            print("❌ 英语翻译失败")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 多语言翻译功能快速测试")
    print("请确保Flask应用正在运行...")
    
    # 测试俄语翻译
    russian_ok = test_russian_translation()
    
    # 测试英语翻译
    english_ok = test_english_translation()
    
    print(f"\n{'='*50}")
    print("测试结果总结:")
    print(f"俄语翻译: {'✅ 成功' if russian_ok else '❌ 失败'}")
    print(f"英语翻译: {'✅ 成功' if english_ok else '❌ 失败'}")
    
    if russian_ok and english_ok:
        print("\n🎉 多语言翻译功能测试通过！")
        print("✅ 前端语言选择功能正常")
        print("✅ 后端多语言处理正常")
        print("✅ 图文替换功能正常")
    else:
        print("\n⚠️  部分功能需要检查")
    
    print('='*50)

if __name__ == "__main__":
    main()
