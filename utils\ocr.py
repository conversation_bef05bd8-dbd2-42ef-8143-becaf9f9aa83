import easyocr
import pytesseract
import logging
import numpy as np
import cv2
from PIL import Image, ImageStat
import re

logger = logging.getLogger(__name__)

# 全局初始化reader，避免重复加载模型
reader = None

def get_reader():
    """获取OCR读取器，延迟初始化"""
    global reader
    if reader is None:
        try:
            logger.info("初始化EasyOCR读取器...")
            reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 简体中文 + 英文
            logger.info("EasyOCR读取器初始化成功")
        except Exception as e:
            logger.error(f"EasyOCR读取器初始化失败: {str(e)}")
            raise
    return reader

def analyze_text_style_precisely(image_region, text_bbox, original_image=None):
    """精确分析文字区域的样式特征，确保像素级匹配"""
    try:
        # 转换为灰度图像
        if len(image_region.shape) == 3:
            gray_region = cv2.cvtColor(np.array(image_region), cv2.COLOR_RGB2GRAY)
        else:
            gray_region = np.array(image_region)

        x, y, w, h = text_bbox

        # 确保区域有效
        if w <= 0 or h <= 0:
            return {
                'font_size': 16,
                'is_bold': False,
                'font_color': (0, 0, 0),
                'density': 0,
                'stroke_width': 1
            }

        # 二值化处理，使用自适应阈值获得更好效果
        binary = cv2.adaptiveThreshold(gray_region, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 11, 2)

        # 计算文字像素密度和笔画宽度
        text_pixels = np.sum(binary == 0)  # 黑色像素（文字）
        total_pixels = binary.size
        density = text_pixels / total_pixels if total_pixels > 0 else 0

        # 更精确的加粗判断：分析笔画宽度
        stroke_width = 1
        if text_pixels > 0:
            # 使用形态学操作分析笔画
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            eroded = cv2.erode(binary, kernel, iterations=1)
            stroke_pixels = np.sum(eroded == 0)

            if stroke_pixels > 0:
                stroke_ratio = stroke_pixels / text_pixels
                stroke_width = max(1, int(3 * (1 - stroke_ratio)))

        # 基于密度和笔画宽度判断加粗
        is_bold = density > 0.25 or stroke_width > 2

        # 更精确的字体大小估算
        # 基于文字区域高度，考虑字符的实际占用空间
        char_height = h

        # 精确分析文字的实际高度（去除空白）
        text_rows = np.any(binary == 0, axis=1)
        if np.any(text_rows):
            # 找到文字的实际边界
            text_indices = np.where(text_rows)[0]
            text_top = text_indices[0]
            text_bottom = text_indices[-1]
            actual_text_height = text_bottom - text_top + 1

            # 使用实际文字高度，确保1:1匹配
            char_height = actual_text_height

            logger.debug(f"文字高度分析: 区域高度={h} 实际文字高度={actual_text_height} 顶部={text_top} 底部={text_bottom}")
        else:
            char_height = h
            logger.debug(f"未检测到文字像素，使用区域高度: {h}")

        # 字体大小计算：根据对比图分析，大幅增加字体倍数
        # 确保翻译后的字体大小与原图完全匹配
        if char_height >= 35:  # 超大标题（如"产品参数"）
            font_size = max(32, min(72, int(char_height * 2.2)))  # 超大倍数
        elif char_height >= 28:  # 大标题
            font_size = max(28, min(64, int(char_height * 2.0)))  # 大倍数
        elif char_height >= 22:  # 中等标题/重要数字
            font_size = max(22, min(48, int(char_height * 1.8)))  # 增大倍数
        elif char_height >= 18:  # 正文标题
            font_size = max(18, min(36, int(char_height * 1.7)))  # 增大倍数
        else:  # 小字体
            font_size = max(14, min(28, int(char_height * 1.6)))  # 增大倍数

        logger.debug(f"字体大小计算: 字符高度={char_height} -> 字体大小={font_size} (倍数={font_size/char_height:.2f})")

        # 精确提取文字颜色
        font_color = (0, 0, 0)  # 默认黑色

        if len(image_region.shape) == 3 and original_image is not None:
            try:
                # 从原图中提取文字颜色
                orig_region = np.array(original_image)[y:y+h, x:x+w] if original_image else image_region

                # 创建文字像素掩码
                text_mask = binary == 0

                if np.any(text_mask) and len(orig_region.shape) == 3:
                    # 获取文字像素的颜色
                    text_colors = orig_region[text_mask]
                    if len(text_colors) > 0:
                        # 使用中位数颜色，更稳定
                        font_color = tuple(np.median(text_colors, axis=0).astype(int))

                        # 确保颜色值在有效范围内
                        font_color = tuple(max(0, min(255, c)) for c in font_color)
            except Exception as color_error:
                logger.debug(f"颜色提取失败: {str(color_error)}")
                font_color = (0, 0, 0)

        result = {
            'font_size': font_size,
            'is_bold': is_bold,
            'font_color': font_color,
            'density': density,
            'stroke_width': stroke_width,
            'char_height': char_height
        }

        logger.debug(f"样式分析结果: 大小={font_size} 加粗={is_bold} 颜色={font_color} 密度={density:.3f}")
        return result

    except Exception as e:
        logger.error(f"精确样式分析失败: {str(e)}")
        return {
            'font_size': 16,
            'is_bold': False,
            'font_color': (0, 0, 0),
            'density': 0,
            'stroke_width': 1,
            'char_height': 16
        }

def analyze_text_style(image_region, text_bbox):
    """分析文字区域的样式特征（兼容旧接口）"""
    return analyze_text_style_precisely(image_region, text_bbox)

def extract_text_with_tesseract(pil_image):
    """使用Tesseract提取文字和详细样式信息"""
    try:
        # 转换为OpenCV格式
        cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

        # 使用Tesseract获取详细信息
        ocr_data = pytesseract.image_to_data(pil_image, output_type=pytesseract.Output.DICT, lang='chi_sim+eng')

        results = []

        for i in range(len(ocr_data['text'])):
            text = ocr_data['text'][i].strip()
            confidence = int(ocr_data['conf'][i])

            # 过滤低置信度和空文本
            if confidence < 30 or not text:
                continue

            # 获取边界框
            x = ocr_data['left'][i]
            y = ocr_data['top'][i]
            w = ocr_data['width'][i]
            h = ocr_data['height'][i]

            if w <= 0 or h <= 0:
                continue

            # 构建边界框坐标（兼容EasyOCR格式）
            box = [
                [x, y],
                [x + w, y],
                [x + w, y + h],
                [x, y + h]
            ]

            # 提取文字区域进行精确样式分析
            text_region = pil_image.crop((x, y, x + w, y + h))
            style_info = analyze_text_style_precisely(np.array(text_region), (0, 0, w, h), pil_image)

            # 精确判断文字层级（标题、正文等）
            font_size = style_info['font_size']
            is_bold = style_info['is_bold']

            # 多维度判断文字层级，更倾向于识别为标题
            is_title = False

            # 1. 基于字体大小判断（进一步降低标题阈值）
            if font_size >= 22:  # 大标题（进一步降低阈值）
                is_title = True
            elif font_size >= 16 and is_bold:  # 中等加粗标题（进一步降低阈值）
                is_title = True
            # 2. 基于内容判断（增加更多关键词）
            elif any(keyword in text for keyword in ['参数', '产品', 'PRODUCT', 'PARAMETERS', '名称', '规格', '颜色', '电压', '频率', '档位', '功能', '220V', '50Hz', '6档', 'V', 'Hz', 'OHZ']):
                is_title = True
            # 3. 基于位置判断（扩大标题区域）
            elif y < height * 0.4 and font_size >= 18:  # 图片顶部40%区域的大文字
                is_title = True
            # 4. 基于文字特征（短文字更可能是标题）
            elif len(text.strip()) <= 10 and font_size >= 16:  # 短文字且字体较大
                is_title = True
            # 5. 数字和单位特殊处理
            elif any(char.isdigit() for char in text) and any(unit in text for unit in ['V', 'Hz', 'OHZ', '档', '速']):
                is_title = True  # 重要数字参数

            text_level = 'title' if is_title else 'body'

            # 强制某些重要文字为加粗
            if is_title or any(keyword in text for keyword in ['参数', '产品', 'PRODUCT', 'PARAMETERS', '220', '50', 'Hz', 'V']):
                is_bold = True
                style_info['is_bold'] = True

            logger.debug(f"文字层级判断: 大小={font_size} 加粗={is_bold} 位置=({x},{y}) 内容='{text[:10]}...' -> {text_level}")

            result = {
                'box': box,
                'text': text,
                'confidence': confidence,
                'style': style_info,
                'level': text_level,
                'bbox': (x, y, w, h)
            }

            results.append(result)
            logger.debug(f"提取文字: {text[:20]}... 样式: {style_info} 层级: {text_level}")

        logger.info(f"Tesseract识别完成，找到 {len(results)} 个文本区域")
        return results

    except Exception as e:
        logger.error(f"Tesseract OCR失败: {str(e)}")
        return []

def extract_text(pil_image, use_detailed_ocr=True):
    """
    从PIL图像中提取文字，支持详细样式信息

    Args:
        pil_image: PIL Image对象
        use_detailed_ocr: 是否使用详细OCR（Tesseract），包含样式信息

    Returns:
        list: 详细OCR时返回包含样式信息的字典列表，否则返回[(box, text), ...]格式

    Raises:
        Exception: OCR处理失败时抛出异常
    """
    try:
        if not isinstance(pil_image, Image.Image):
            raise ValueError("输入必须是PIL Image对象")

        # 检查图像尺寸
        width, height = pil_image.size
        if width < 10 or height < 10:
            logger.warning(f"图像尺寸过小: {width}x{height}")
            return []

        if width > 4000 or height > 4000:
            logger.info(f"图像尺寸较大: {width}x{height}，可能需要较长处理时间")

        logger.info(f"开始OCR识别，图像尺寸: {width}x{height}")

        # 优先使用详细OCR（Tesseract）
        if use_detailed_ocr:
            try:
                detailed_results = extract_text_with_tesseract(pil_image)
                if detailed_results:
                    logger.info(f"使用Tesseract OCR，识别到 {len(detailed_results)} 个文本区域")
                    return detailed_results
                else:
                    logger.warning("Tesseract OCR未识别到文字，回退到EasyOCR")
            except Exception as e:
                logger.warning(f"Tesseract OCR失败: {str(e)}，回退到EasyOCR")

        # 回退到EasyOCR
        image_array = np.array(pil_image)
        ocr_reader = get_reader()
        results = ocr_reader.readtext(image_array)

        if not results:
            logger.info("EasyOCR未识别到任何文字")
            return []

        # 处理EasyOCR结果
        processed_results = []
        for item in results:
            try:
                if len(item) >= 3:
                    box, text, confidence = item

                    # 过滤置信度过低的结果
                    if confidence < 0.3:
                        logger.debug(f"跳过低置信度文本: {text} (置信度: {confidence:.2f})")
                        continue

                    # 过滤空文本或只包含空白字符的文本
                    if not text or not text.strip():
                        logger.debug("跳过空文本")
                        continue

                    # 验证box格式
                    if not isinstance(box, (list, tuple)) or len(box) < 4:
                        logger.warning(f"无效的box格式: {box}")
                        continue

                    if use_detailed_ocr:
                        # 为EasyOCR结果添加基本样式信息
                        x_coords = [point[0] for point in box]
                        y_coords = [point[1] for point in box]
                        x_min, x_max = min(x_coords), max(x_coords)
                        y_min, y_max = min(y_coords), max(y_coords)
                        w, h = x_max - x_min, y_max - y_min

                        # 基本样式估算
                        font_size = max(12, min(72, int(h * 0.8)))
                        is_bold = h > 30  # 简单的加粗判断

                        result = {
                            'box': box,
                            'text': text.strip(),
                            'confidence': int(confidence * 100),
                            'style': {
                                'font_size': font_size,
                                'is_bold': is_bold,
                                'font_color': (0, 0, 0),
                                'density': 0.2
                            },
                            'level': 'title' if is_bold else 'body',
                            'bbox': (int(x_min), int(y_min), int(w), int(h))
                        }
                        processed_results.append(result)
                    else:
                        processed_results.append((box, text.strip()))

                    logger.debug(f"识别到文本: {text.strip()} (置信度: {confidence:.2f})")
                else:
                    logger.warning(f"OCR结果格式异常: {item}")

            except Exception as e:
                logger.error(f"处理OCR结果项时出错: {str(e)}, 项目: {item}")
                continue

        logger.info(f"EasyOCR识别完成，有效文本区域: {len(processed_results)}")
        return processed_results

    except Exception as e:
        logger.error(f"OCR识别过程中出错: {str(e)}")
        raise Exception(f"OCR识别失败: {str(e)}")
