from google.cloud import translate_v2 as translate
from google.api_core import exceptions as google_exceptions
import os
import logging
import time
import re

logger = logging.getLogger(__name__)

# 设置 GOOGLE 凭证路径
credentials_path = "credentials.json"
if os.path.exists(credentials_path):
    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = credentials_path
    logger.info(f"使用Google凭证文件: {credentials_path}")
else:
    logger.warning(f"Google凭证文件不存在: {credentials_path}")

# 全局客户端，延迟初始化
client = None

def get_translate_client():
    """获取翻译客户端，延迟初始化"""
    global client
    if client is None:
        try:
            logger.info("初始化Google翻译客户端...")
            client = translate.Client()
            logger.info("Google翻译客户端初始化成功")
        except Exception as e:
            logger.error(f"Google翻译客户端初始化失败: {str(e)}")
            raise
    return client

def is_chinese_text(text):
    """检查文本是否主要是中文"""
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
    return len(chinese_chars) > len(text) * 0.5

def translate_text(text, target_lang='ru', max_retries=3):
    """
    翻译文本

    Args:
        text: 要翻译的文本
        target_lang: 目标语言代码
        max_retries: 最大重试次数

    Returns:
        str: 翻译后的文本

    Raises:
        Exception: 翻译失败时抛出异常
    """
    if not text or not text.strip():
        logger.debug("输入文本为空，返回空字符串")
        return ''

    text = text.strip()

    # 如果目标语言是中文，且输入已经是中文，直接返回
    if target_lang in ['zh-CN', 'zh', 'zh-Hans'] and is_chinese_text(text):
        logger.debug(f"文本已经是中文，直接返回: {text[:50]}...")
        return text

    # 语言代码映射（确保使用正确的Google翻译语言代码）
    language_mapping = {
        'ru': 'ru',      # 俄语
        'en': 'en',      # 英语
        'ja': 'ja',      # 日语
        'ko': 'ko',      # 韩语
        'fr': 'fr',      # 法语
        'de': 'de',      # 德语
        'es': 'es',      # 西班牙语
        'it': 'it',      # 意大利语
        'pt': 'pt',      # 葡萄牙语
        'ar': 'ar',      # 阿拉伯语
        'th': 'th',      # 泰语
        'vi': 'vi',      # 越南语
        'zh-CN': 'zh-CN', # 简体中文
        'zh': 'zh-CN',   # 中文
    }

    # 获取正确的语言代码
    google_lang_code = language_mapping.get(target_lang, target_lang)

    # 检查文本长度
    if len(text) > 5000:
        logger.warning(f"文本长度过长 ({len(text)} 字符)，可能影响翻译质量")

    for attempt in range(max_retries):
        try:
            logger.debug(f"开始翻译 (尝试 {attempt + 1}/{max_retries}): {text[:50]}...")

            translate_client = get_translate_client()
            result = translate_client.translate(
                text,
                target_language=google_lang_code,
                format_='text'
            )

            if not result or 'translatedText' not in result:
                raise Exception("翻译API返回格式异常")

            translated_text = result['translatedText']

            if not translated_text:
                logger.warning("翻译结果为空，返回原文")
                return text

            logger.debug(f"翻译成功: {text[:30]}... -> {translated_text[:30]}...")
            return translated_text

        except google_exceptions.Forbidden as e:
            logger.error(f"Google翻译API权限被拒绝: {str(e)}")
            raise Exception("翻译服务权限不足，请检查API密钥")

        except google_exceptions.ResourceExhausted as e:
            logger.error(f"Google翻译API配额耗尽: {str(e)}")
            raise Exception("翻译服务配额已用完")

        except google_exceptions.Unauthenticated as e:
            logger.error(f"Google翻译API认证失败: {str(e)}")
            raise Exception("翻译服务认证失败，请检查凭证文件")

        except google_exceptions.ServiceUnavailable as e:
            logger.warning(f"Google翻译服务暂时不可用 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # 指数退避
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                continue
            else:
                raise Exception("翻译服务暂时不可用")

        except Exception as e:
            logger.error(f"翻译过程中出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                wait_time = 1
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                continue
            else:
                logger.error(f"翻译最终失败，返回原文: {text}")
                return text  # 翻译失败时返回原文而不是抛出异常
