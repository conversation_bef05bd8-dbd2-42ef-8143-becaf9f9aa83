#!/usr/bin/env python3
"""
精确样式匹配测试脚本 - 验证像素级样式还原
"""

import requests
from PIL import Image, ImageDraw, ImageFont
import io
import os

BASE_URL = "http://localhost:5000"

def create_complex_layout_image():
    """创建复杂排版的测试图片，模拟产品参数页面"""
    image = Image.new('RGB', (800, 600), (255, 255, 255))
    draw = ImageDraw.Draw(image)
    
    try:
        # 尝试加载不同大小的字体
        title_font = None
        body_font = None
        small_font = None
        
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            "/System/Library/Fonts/Arial.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    title_font = ImageFont.truetype(font_path, 32)
                    body_font = ImageFont.truetype(font_path, 18)
                    small_font = ImageFont.truetype(font_path, 14)
                    break
                except:
                    continue
        
        if not title_font:
            title_font = ImageFont.load_default()
            body_font = ImageFont.load_default()
            small_font = ImageFont.load_default()
    
    except:
        title_font = ImageFont.load_default()
        body_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # 绘制标题（大字体，加粗效果）
    title_text = "Product Specifications"
    draw.text((50, 30), title_text, fill=(0, 0, 0), font=title_font)
    # 模拟加粗效果
    draw.text((51, 30), title_text, fill=(0, 0, 0), font=title_font)
    
    # 绘制分隔线
    draw.line([(50, 80), (750, 80)], fill=(128, 128, 128), width=2)
    
    # 绘制参数表格
    y_pos = 120
    
    # 参数项目（模拟不同层级的文字）
    params = [
        ("Model Number:", "XYZ-2024", True),  # 标签：加粗
        ("Dimensions:", "120 x 80 x 25 mm", False),  # 值：正常
        ("Weight:", "250 grams", False),
        ("Material:", "Aluminum Alloy", False),
        ("Color Options:", "Black, Silver, Blue", False),
        ("Warranty:", "2 Years Limited", True),  # 重要信息：加粗
        ("Certification:", "CE, FCC, RoHS", False),
        ("Operating Temp:", "-10°C to +60°C", False),
    ]
    
    for label, value, is_bold in params:
        # 绘制标签
        if is_bold:
            # 模拟加粗
            draw.text((50, y_pos), label, fill=(0, 0, 0), font=body_font)
            draw.text((51, y_pos), label, fill=(0, 0, 0), font=body_font)
        else:
            draw.text((50, y_pos), label, fill=(0, 0, 0), font=body_font)
        
        # 绘制值
        draw.text((250, y_pos), value, fill=(64, 64, 64), font=body_font)
        
        y_pos += 35
    
    # 绘制底部注释（小字体）
    note_text = "* Specifications subject to change without notice"
    draw.text((50, 520), note_text, fill=(128, 128, 128), font=small_font)
    
    # 绘制边框
    draw.rectangle([(30, 20), (770, 560)], outline=(200, 200, 200), width=1)
    
    return image

def create_title_body_image():
    """创建标题+正文的简单测试图片"""
    image = Image.new('RGB', (600, 400), (255, 255, 255))
    draw = ImageDraw.Draw(image)
    
    try:
        # 加载字体
        title_font = None
        body_font = None
        
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "/System/Library/Fonts/Arial.ttf",
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    title_font = ImageFont.truetype(font_path, 28)
                    body_font = ImageFont.truetype(font_path, 16)
                    break
                except:
                    continue
        
        if not title_font:
            title_font = ImageFont.load_default()
            body_font = ImageFont.load_default()
    
    except:
        title_font = ImageFont.load_default()
        body_font = ImageFont.load_default()
    
    # 绘制标题（加粗效果）
    title = "Welcome to Our Service"
    draw.text((50, 50), title, fill=(0, 0, 0), font=title_font)
    draw.text((51, 50), title, fill=(0, 0, 0), font=title_font)  # 模拟加粗
    
    # 绘制正文
    body_lines = [
        "This is a sample document",
        "with multiple text levels",
        "for testing translation",
        "and style preservation."
    ]
    
    y_pos = 120
    for line in body_lines:
        draw.text((50, y_pos), line, fill=(64, 64, 64), font=body_font)
        y_pos += 30
    
    return image

def test_precise_style_matching(test_name, image, language_code, language_name):
    """测试精确样式匹配"""
    print(f"\n{'='*60}")
    print(f"测试: {test_name} -> {language_name}")
    print('='*60)
    
    try:
        # 保存输入图片
        input_filename = f'input_{test_name}_{language_code}.png'
        image.save(input_filename)
        print(f"✅ 输入图片已保存: {input_filename}")
        
        # 转换为字节流
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        # 发送翻译请求
        files = {'image': (input_filename, img_byte_arr, 'image/png')}
        data = {'target_language': language_code}
        
        print(f"发送精确样式翻译请求...")
        response = requests.post(f"{BASE_URL}/", files=files, data=data)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            if 'image/png' in response.headers.get('content-type', ''):
                # 保存翻译后的图片
                output_filename = f'precise_output_{test_name}_{language_code}.png'
                with open(output_filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ 精确样式翻译成功！")
                print(f"✅ 输出图片: {output_filename}")
                print(f"✅ 文件大小: {len(response.content)} 字节")
                
                # 分析输出图片
                output_image = Image.open(output_filename)
                input_size = image.size
                output_size = output_image.size
                
                print(f"📊 图片尺寸: {input_size} -> {output_size}")
                
                if input_size == output_size:
                    print("✅ 图片尺寸保持一致")
                else:
                    print("⚠️  图片尺寸发生变化")
                
                return True
                
            elif 'application/json' in response.headers.get('content-type', ''):
                # JSON响应
                try:
                    import json
                    data = response.json()
                    if 'message' in data and '未检测到文字' in data['message']:
                        print(f"⚠️  OCR未检测到文字，可能需要调整图片")
                        return False
                    else:
                        print(f"📄 响应: {data}")
                        return False
                except:
                    print(f"📄 响应: {response.text}")
                    return False
            else:
                print(f"❌ 未知响应类型")
                return False
        else:
            print(f"❌ 翻译失败")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 精确样式匹配测试")
    print("验证像素级样式还原功能...")
    
    # 测试用例
    test_cases = [
        ("complex_layout", create_complex_layout_image(), "复杂排版测试"),
        ("title_body", create_title_body_image(), "标题正文测试"),
    ]
    
    # 测试语言
    languages = [
        ('ru', '俄语'),
        ('ja', '日语'),
        ('fr', '法语'),
    ]
    
    total_tests = len(test_cases) * len(languages)
    passed_tests = 0
    
    for test_name, test_image, test_desc in test_cases:
        print(f"\n🔧 {test_desc}")
        
        for lang_code, lang_name in languages:
            if test_precise_style_matching(test_name, test_image, lang_code, lang_name):
                passed_tests += 1
    
    print(f"\n{'='*80}")
    print(f"精确样式匹配测试结果: {passed_tests}/{total_tests}")
    print('='*80)
    
    if passed_tests == total_tests:
        print("🎉 所有精确样式测试通过！")
        print("✅ 像素级样式匹配功能正常")
        print("✅ 文字擦除功能精确")
        print("✅ 样式继承功能完整")
        print("✅ 多语言字体支持正常")
    elif passed_tests > total_tests * 0.7:
        print(f"⚠️  大部分测试通过 ({passed_tests}/{total_tests})")
        print("可能需要微调某些语言的字体配置")
    else:
        print(f"❌ 需要检查精确样式匹配功能")
    
    print(f"\n📁 生成的测试文件:")
    for test_name, _, _ in test_cases:
        for lang_code, _ in languages:
            input_file = f'input_{test_name}_{lang_code}.png'
            output_file = f'precise_output_{test_name}_{lang_code}.png'
            if os.path.exists(input_file):
                print(f"- {input_file} (输入)")
            if os.path.exists(output_file):
                print(f"- {output_file} (输出)")

if __name__ == "__main__":
    main()
