#!/usr/bin/env python3
"""
多语言翻译功能测试脚本
"""

import requests
from PIL import Image, ImageDraw, ImageFont
import io
import os

BASE_URL = "http://localhost:5000"

def create_chinese_test_image():
    """创建包含中文的测试图片"""
    image = Image.new('RGB', (600, 300), (255, 255, 255))
    draw = ImageDraw.Draw(image)
    
    try:
        # 尝试使用中文字体
        font_paths = [
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/simhei.ttf",
            "/System/Library/Fonts/PingFang.ttc",
            "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc"
        ]
        
        font = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 32)
                    break
                except:
                    continue
        
        if font is None:
            font = ImageFont.load_default()
        
    except:
        font = ImageFont.load_default()
    
    # 绘制中文文字
    texts = [
        ("欢迎使用", 50, 50),
        ("图片翻译工具", 50, 120),
        ("多语言支持", 50, 190),
    ]
    
    for text, x, y in texts:
        draw.text((x, y), text, fill=(0, 0, 0), font=font)
    
    return image

def test_language_translation(language_code, language_name):
    """测试特定语言的翻译"""
    print(f"\n{'='*50}")
    print(f"测试语言: {language_name} ({language_code})")
    print('='*50)
    
    try:
        # 创建测试图片
        image = create_chinese_test_image()
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr.seek(0)
        
        # 发送请求
        files = {'image': ('chinese_test.png', img_byte_arr, 'image/png')}
        data = {'target_language': language_code}
        
        print(f"发送翻译请求...")
        response = requests.post(f"{BASE_URL}/", files=files, data=data)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            if 'image/png' in response.headers.get('content-type', ''):
                # 保存翻译后的图片
                output_filename = f'translated_{language_code}.png'
                with open(output_filename, 'wb') as f:
                    f.write(response.content)
                print(f"✅ 翻译成功！保存为: {output_filename}")
                print(f"文件大小: {len(response.content)} 字节")
                return True
            else:
                print(f"✅ 响应成功，但内容类型异常")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ 翻译失败")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_all_languages():
    """测试所有支持的语言"""
    languages = [
        ('ru', '俄语'),
        ('en', '英语'),
        ('ja', '日语'),
        ('ko', '韩语'),
        ('fr', '法语'),
        ('de', '德语'),
        ('es', '西班牙语'),
        ('it', '意大利语'),
        ('pt', '葡萄牙语'),
        ('ar', '阿拉伯语'),
        ('th', '泰语'),
        ('vi', '越南语'),
    ]
    
    print("🚀 开始多语言翻译测试...")
    
    success_count = 0
    total_count = len(languages)
    
    for lang_code, lang_name in languages:
        if test_language_translation(lang_code, lang_name):
            success_count += 1
    
    print(f"\n{'='*60}")
    print(f"测试结果: {success_count}/{total_count} 成功")
    print('='*60)
    
    if success_count == total_count:
        print("🎉 所有语言测试通过！")
    else:
        print(f"⚠️  有 {total_count - success_count} 个语言测试失败")
    
    return success_count == total_count

def test_frontend_interface():
    """测试前端界面"""
    print("\n测试前端界面...")
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"前端页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 检查是否包含语言选择元素
            content = response.text
            if 'target_language' in content and 'select' in content:
                print("✅ 前端页面包含语言选择功能")
                if '俄语' in content:
                    print("✅ 默认俄语选项存在")
                    return True
                else:
                    print("❌ 未找到俄语选项")
                    return False
            else:
                print("❌ 前端页面缺少语言选择功能")
                return False
        else:
            print("❌ 前端页面访问失败")
            return False
            
    except Exception as e:
        print(f"❌ 前端测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 多语言图片翻译功能测试")
    print("请确保Flask应用正在运行 (python app.py)")
    print("然后按Enter键开始测试...")
    input()
    
    # 测试前端界面
    frontend_ok = test_frontend_interface()
    
    if frontend_ok:
        # 测试多语言翻译
        all_ok = test_all_languages()
        
        if all_ok:
            print("\n🎯 功能验证总结:")
            print("✅ 前端多语言选择界面正常")
            print("✅ 后端接收语言参数正常")
            print("✅ 多语言翻译功能正常")
            print("✅ 图文替换功能正常")
            print("✅ 所有测试语言都能正确处理")
        else:
            print("\n⚠️  部分功能需要进一步检查")
    else:
        print("\n❌ 前端界面测试失败，请检查模板文件")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
