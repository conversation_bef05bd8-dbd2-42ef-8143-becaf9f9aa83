"""
图像处理模块：实现文字擦除和替换功能
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import logging

logger = logging.getLogger(__name__)

class ImageTextProcessor:
    """图像文字处理器"""
    
    def __init__(self):
        self.font_cache = {}
    
    def get_font_for_language(self, language_code, size=20, is_bold=False):
        """根据语言获取合适的字体，支持加粗"""
        cache_key = f"{language_code}_{size}_{is_bold}"
        if cache_key in self.font_cache:
            return self.font_cache[cache_key]
        
        # 不同语言的字体路径（支持加粗）
        font_paths = {
            'ru': [  # 俄语字体
                "C:/Windows/Fonts/arialbd.ttf" if is_bold else "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibrib.ttf" if is_bold else "C:/Windows/Fonts/calibri.ttf",
                "C:/Windows/Fonts/timesbd.ttf" if is_bold else "C:/Windows/Fonts/times.ttf",
                "/System/Library/Fonts/Arial Bold.ttf" if is_bold else "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf" if is_bold else "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ],
            'ja': [  # 日语字体
                "C:/Windows/Fonts/msgothic.ttc",
                "C:/Windows/Fonts/meiryo.ttc",
                "/System/Library/Fonts/Hiragino Sans GB.ttc",
                "/usr/share/fonts/truetype/takao-gothic/TakaoGothic.ttf",
            ],
            'ko': [  # 韩语字体
                "C:/Windows/Fonts/malgun.ttf",
                "C:/Windows/Fonts/gulim.ttc",
                "/System/Library/Fonts/AppleSDGothicNeo.ttc",
                "/usr/share/fonts/truetype/nanum/NanumGothic.ttf",
            ],
            'ar': [  # 阿拉伯语字体
                "C:/Windows/Fonts/tahoma.ttf",
                "C:/Windows/Fonts/arial.ttf",
                "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ],
            'default': [  # 默认字体
                "C:/Windows/Fonts/arialbd.ttf" if is_bold else "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibrib.ttf" if is_bold else "C:/Windows/Fonts/calibri.ttf",
                "/System/Library/Fonts/Arial Bold.ttf" if is_bold else "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf" if is_bold else "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ]
        }
        
        # 获取对应语言的字体路径，如果没有则使用默认
        paths = font_paths.get(language_code, font_paths['default'])
        
        font = None
        for font_path in paths:
            try:
                import os
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, size=size)
                    logger.info(f"成功加载字体: {font_path} (语言: {language_code})")
                    break
            except Exception as e:
                logger.debug(f"尝试加载字体 {font_path} 失败: {str(e)}")
                continue
        
        if font is None:
            logger.warning(f"无法加载 {language_code} 字体，使用默认字体")
            font = ImageFont.load_default()
        
        self.font_cache[cache_key] = font
        return font
    
    def analyze_background_precisely(self, image, text_bbox):
        """精确分析文字区域的背景色和纹理"""
        try:
            img_array = np.array(image)
            x, y, w, h = text_bbox

            # 确保坐标在图像范围内
            img_h, img_w = img_array.shape[:2]
            x = max(0, min(x, img_w - 1))
            y = max(0, min(y, img_h - 1))
            w = min(w, img_w - x)
            h = min(h, img_h - y)

            if w <= 0 or h <= 0:
                return (255, 255, 255), 'solid'

            # 扩展区域获取周围背景
            margin = max(5, min(w, h) // 4)
            x_start = max(0, x - margin)
            y_start = max(0, y - margin)
            x_end = min(img_w, x + w + margin)
            y_end = min(img_h, y + h + margin)

            # 提取扩展区域
            extended_region = img_array[y_start:y_end, x_start:x_end]

            # 创建文字区域掩码
            mask = np.zeros((y_end - y_start, x_end - x_start), dtype=np.uint8)
            text_x = x - x_start
            text_y = y - y_start
            mask[text_y:text_y + h, text_x:text_x + w] = 255

            # 获取背景像素（非文字区域）
            bg_mask = mask == 0
            if np.any(bg_mask):
                bg_pixels = extended_region[bg_mask]

                # 使用更精确的聚类分析
                from sklearn.cluster import KMeans

                if len(bg_pixels) > 100:
                    # 采样减少计算量
                    sample_size = min(1000, len(bg_pixels))
                    indices = np.random.choice(len(bg_pixels), sample_size, replace=False)
                    sampled_pixels = bg_pixels[indices]
                else:
                    sampled_pixels = bg_pixels

                # K-means聚类找主要背景色
                n_clusters = min(5, len(sampled_pixels) // 10 + 1)
                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                kmeans.fit(sampled_pixels)

                # 选择最常见的颜色作为背景色
                labels = kmeans.labels_
                unique_labels, counts = np.unique(labels, return_counts=True)
                most_common_label = unique_labels[np.argmax(counts)]
                bg_color = kmeans.cluster_centers_[most_common_label].astype(int)

                # 分析背景纹理类型
                bg_std = np.std(sampled_pixels, axis=0)
                texture_type = 'gradient' if np.mean(bg_std) > 20 else 'solid'

                return tuple(bg_color), texture_type
            else:
                return (255, 255, 255), 'solid'

        except Exception as e:
            logger.warning(f"精确背景分析失败: {str(e)}")
            return (255, 255, 255), 'solid'

    def estimate_background_color(self, image, text_region):
        """估算文字区域的背景色（兼容旧接口）"""
        try:
            # 转换text_region为bbox格式
            x_coords = [point[0] for point in text_region]
            y_coords = [point[1] for point in text_region]
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            bbox = (int(x_min), int(y_min), int(x_max - x_min), int(y_max - y_min))

            bg_color, _ = self.analyze_background_precisely(image, bbox)
            return bg_color

        except Exception as e:
            logger.warning(f"背景色估算失败: {str(e)}，使用白色")
            return (255, 255, 255)  # 默认白色
    
    def erase_text_precisely(self, image, ocr_results):
        """使用OpenCV inpaint算法彻底擦除文字，确保无残留"""
        try:
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            img_height, img_width = cv_image.shape[:2]

            # 创建全局掩码，包含所有文字区域
            global_mask = np.zeros((img_height, img_width), dtype=np.uint8)

            logger.info(f"开始创建文字擦除掩码，共 {len(ocr_results)} 个文字区域")

            for i, result in enumerate(ocr_results):
                try:
                    # 获取文字区域信息
                    if isinstance(result, dict):
                        bbox = result['bbox']
                        text_region = result['box']
                        text = result.get('text', '')
                    else:
                        # 兼容旧格式
                        text_region = result[0]
                        text = result[1] if len(result) > 1 else ''
                        x_coords = [point[0] for point in text_region]
                        y_coords = [point[1] for point in text_region]
                        x_min, x_max = min(x_coords), max(x_coords)
                        y_min, y_max = min(y_coords), max(y_coords)
                        bbox = (int(x_min), int(y_min), int(x_max - x_min), int(y_max - y_min))

                    x, y, w, h = bbox

                    # 确保坐标在图像范围内
                    x = max(0, min(x, img_width - 1))
                    y = max(0, min(y, img_height - 1))
                    w = min(w, img_width - x)
                    h = min(h, img_height - y)

                    if w <= 0 or h <= 0:
                        logger.warning(f"跳过无效文字区域 {i}: {bbox}")
                        continue

                    # 扩展擦除区域，确保完全覆盖文字
                    margin = max(2, min(w, h) // 10)  # 动态边距
                    x_expanded = max(0, x - margin)
                    y_expanded = max(0, y - margin)
                    w_expanded = min(img_width - x_expanded, w + 2 * margin)
                    h_expanded = min(img_height - y_expanded, h + 2 * margin)

                    # 在全局掩码上标记文字区域
                    cv2.rectangle(global_mask,
                                (x_expanded, y_expanded),
                                (x_expanded + w_expanded, y_expanded + h_expanded),
                                255, -1)

                    logger.debug(f"添加文字区域 {i} 到掩码: {text[:20]}... 区域: ({x_expanded}, {y_expanded}, {w_expanded}, {h_expanded})")

                except Exception as e:
                    logger.error(f"处理文字区域 {i} 时出错: {str(e)}")
                    continue

            # 使用OpenCV的inpaint算法进行智能修复
            logger.info("开始使用inpaint算法修复文字区域...")

            # 使用Telea算法，适合复杂纹理背景
            inpainted_image = cv2.inpaint(cv_image, global_mask, 3, cv2.INPAINT_TELEA)

            # 如果Telea效果不好，尝试NS算法
            if np.array_equal(inpainted_image, cv_image):
                logger.info("Telea算法无效果，尝试NS算法...")
                inpainted_image = cv2.inpaint(cv_image, global_mask, 3, cv2.INPAINT_NS)

            # 对修复区域进行轻微模糊，使其更自然
            kernel_size = 3
            blurred = cv2.GaussianBlur(inpainted_image, (kernel_size, kernel_size), 0)

            # 只在掩码区域应用模糊
            mask_3channel = cv2.cvtColor(global_mask, cv2.COLOR_GRAY2BGR)
            mask_normalized = mask_3channel.astype(np.float32) / 255.0

            final_image = inpainted_image.astype(np.float32) * (1 - mask_normalized) + \
                         blurred.astype(np.float32) * mask_normalized
            final_image = final_image.astype(np.uint8)

            # 转换回PIL格式
            result_image = Image.fromarray(cv2.cvtColor(final_image, cv2.COLOR_BGR2RGB))

            logger.info("文字擦除完成，使用inpaint算法确保无残留")
            return result_image

        except Exception as e:
            logger.error(f"精确文字擦除失败: {str(e)}")
            return image

    def _inpaint_text_region(self, cv_image, bbox):
        """使用图像修复技术填充文字区域"""
        try:
            x, y, w, h = bbox

            # 创建掩码
            mask = np.zeros(cv_image.shape[:2], dtype=np.uint8)
            mask[y:y+h, x:x+w] = 255

            # 使用OpenCV的图像修复
            inpainted = cv2.inpaint(cv_image, mask, 3, cv2.INPAINT_TELEA)

            # 将修复结果应用到原图
            cv_image[y:y+h, x:x+w] = inpainted[y:y+h, x:x+w]

        except Exception as e:
            logger.warning(f"图像修复失败: {str(e)}")

    def _smooth_edges(self, cv_image, bbox):
        """平滑擦除区域的边缘"""
        try:
            x, y, w, h = bbox

            # 创建边缘区域掩码
            edge_mask = np.zeros(cv_image.shape[:2], dtype=np.uint8)

            # 边缘宽度
            edge_width = 2

            # 上下边缘
            edge_mask[max(0, y-edge_width):y+edge_width, x:x+w] = 255
            edge_mask[y+h-edge_width:min(cv_image.shape[0], y+h+edge_width), x:x+w] = 255

            # 左右边缘
            edge_mask[y:y+h, max(0, x-edge_width):x+edge_width] = 255
            edge_mask[y:y+h, x+w-edge_width:min(cv_image.shape[1], x+w+edge_width)] = 255

            # 对边缘区域进行轻微模糊
            blurred = cv2.GaussianBlur(cv_image, (3, 3), 0)
            cv_image[edge_mask > 0] = blurred[edge_mask > 0]

        except Exception as e:
            logger.warning(f"边缘平滑失败: {str(e)}")

    def erase_text_region(self, image, text_regions):
        """擦除文字区域（兼容旧接口）"""
        # 转换为新格式
        ocr_results = []
        for region in text_regions:
            x_coords = [point[0] for point in region]
            y_coords = [point[1] for point in region]
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            bbox = (int(x_min), int(y_min), int(x_max - x_min), int(y_max - y_min))

            ocr_results.append({
                'box': region,
                'bbox': bbox
            })

        return self.erase_text_precisely(image, ocr_results)
    
    def calculate_text_size(self, text, font):
        """计算文字尺寸"""
        # 创建临时图像来测量文字
        temp_img = Image.new('RGB', (1, 1))
        draw = ImageDraw.Draw(temp_img)
        bbox = draw.textbbox((0, 0), text, font=font)
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]
        return width, height
    
    def get_precise_font_match(self, text, style_info, language_code, target_bbox):
        """获取像素级精确匹配原文样式的字体"""
        try:
            # 从样式信息中获取精确参数
            original_size = style_info.get('font_size', 16)
            is_bold = style_info.get('is_bold', False)
            char_height = style_info.get('char_height', original_size)
            stroke_width = style_info.get('stroke_width', 1)

            target_width, target_height = target_bbox[2], target_bbox[3]

            logger.debug(f"精确字体匹配: 原始大小={original_size} 字符高度={char_height} 加粗={is_bold}")

            # 根据对比图分析，大幅增加字体倍数以完全匹配原图效果
            # 确保翻译后的字体大小与原图1:1匹配
            if char_height >= 35:  # 超大标题（如"产品参数"）
                base_size = max(32, min(72, int(char_height * 2.2)))  # 超大倍数
                logger.debug(f"超大标题字体: 字符高度={char_height} -> 基础大小={base_size}")
            elif char_height >= 28:  # 大标题
                base_size = max(28, min(64, int(char_height * 2.0)))  # 大倍数
                logger.debug(f"大标题字体: 字符高度={char_height} -> 基础大小={base_size}")
            elif char_height >= 22:  # 中等标题/重要数字（如220V, 50Hz）
                base_size = max(22, min(48, int(char_height * 1.8)))  # 增大倍数
                logger.debug(f"中等标题/数字字体: 字符高度={char_height} -> 基础大小={base_size}")
            elif char_height >= 18:  # 正文标题
                base_size = max(18, min(36, int(char_height * 1.7)))  # 增大倍数
                logger.debug(f"正文标题字体: 字符高度={char_height} -> 基础大小={base_size}")
            else:  # 小字体
                base_size = max(14, min(28, int(char_height * 1.6)))  # 增大倍数
                logger.debug(f"小字体: 字符高度={char_height} -> 基础大小={base_size}")

            # 根据加粗状态微调
            if is_bold:
                # 加粗文字需要更大以保持视觉冲击力
                base_size = int(base_size * 1.3)  # 增大加粗倍数
                logger.debug(f"加粗文字增大: {base_size}")

            # 获取字体
            font = self.get_font_for_language(language_code, base_size, is_bold)
            text_width, text_height = self.calculate_text_size(text, font)

            # 最大化保持字体大小，几乎不进行调整
            max_attempts = 1  # 最小化调整次数
            attempt = 0

            while attempt < max_attempts:
                # 检查是否需要调整
                width_ratio = text_width / target_width if target_width > 0 else 1
                height_ratio = text_height / target_height if target_height > 0 else 1

                # 极大放宽匹配条件，几乎不调整字体大小
                if 0.3 <= width_ratio <= 2.0 and 0.3 <= height_ratio <= 2.0:
                    logger.debug(f"字体大小保持原始: 宽度比={width_ratio:.2f} 高度比={height_ratio:.2f}")
                    break

                # 只有在极端极端不匹配时才调整
                if width_ratio > 2.5 or height_ratio > 2.5:
                    # 文字太大，轻微缩小
                    scale = min(0.8 / width_ratio, 0.8 / height_ratio)
                elif width_ratio < 0.2 or height_ratio < 0.2:
                    # 文字太小，轻微放大
                    scale = min(0.4 / width_ratio, 0.4 / height_ratio, 1.5)
                else:
                    # 其他情况，保持原大小
                    logger.debug(f"字体大小完全保持原始: {base_size}")
                    break

                new_size = max(8, min(72, int(base_size * scale)))

                if new_size == base_size:
                    break  # 避免无限循环

                base_size = new_size
                font = self.get_font_for_language(language_code, base_size, is_bold)
                text_width, text_height = self.calculate_text_size(text, font)

                attempt += 1
                logger.debug(f"字体微调 {attempt}: 大小={base_size} 尺寸=({text_width}x{text_height}) 目标=({target_width}x{target_height})")

            # 获取精确的字体颜色
            font_color = style_info.get('font_color', (0, 0, 0))

            # 确保颜色值有效
            if not isinstance(font_color, (tuple, list)) or len(font_color) != 3:
                font_color = (0, 0, 0)
            else:
                font_color = tuple(max(0, min(255, int(c))) for c in font_color)

            logger.debug(f"最终字体: 大小={base_size} 颜色={font_color} 加粗={is_bold}")
            return font, font_color

        except Exception as e:
            logger.error(f"精确字体匹配失败: {str(e)}")
            # 回退到基本方法
            return self.fit_text_to_region(text, target_bbox[2], target_bbox[3], language_code, is_bold)

    def fit_text_to_region(self, text, region_width, region_height, language_code, is_bold=False):
        """调整文字大小以适应区域"""
        # 从较大的字体开始尝试
        for size in range(min(region_height, 50), 8, -2):
            font = self.get_font_for_language(language_code, size, is_bold)
            text_width, text_height = self.calculate_text_size(text, font)

            # 如果文字能够适应区域，返回这个字体
            if text_width <= region_width * 0.9 and text_height <= region_height * 0.9:
                return font, (0, 0, 0)  # 返回字体和默认颜色

        # 如果都不适应，返回最小字体
        return self.get_font_for_language(language_code, 10, is_bold), (0, 0, 0)
    
    def draw_text_with_pixel_perfect_style(self, image, ocr_results, translated_texts, language_code):
        """使用像素级精确样式绘制翻译文字，确保完全对齐"""
        draw = ImageDraw.Draw(image)

        logger.info(f"开始像素级精确文字绘制，共 {len(ocr_results)} 个文字区域")

        for i, (result, translated_text) in enumerate(zip(ocr_results, translated_texts)):
            try:
                if not translated_text or not translated_text.strip():
                    logger.debug(f"跳过空翻译文字 {i}")
                    continue

                # 获取文字信息
                if isinstance(result, dict):
                    bbox = result['bbox']
                    style_info = result.get('style', {})
                    text_level = result.get('level', 'body')
                    original_text = result.get('text', '')
                else:
                    # 兼容旧格式
                    region = result[0]
                    original_text = result[1] if len(result) > 1 else ''
                    x_coords = [point[0] for point in region]
                    y_coords = [point[1] for point in region]
                    x_min, x_max = min(x_coords), max(x_coords)
                    y_min, y_max = min(y_coords), max(y_coords)
                    bbox = (int(x_min), int(y_min), int(x_max - x_min), int(y_max - y_min))
                    style_info = {'font_size': 16, 'is_bold': False, 'font_color': (0, 0, 0)}
                    text_level = 'body'

                x, y, w, h = bbox

                logger.debug(f"处理文字 {i}: '{original_text[:20]}...' -> '{translated_text[:20]}...' "
                           f"区域: ({x}, {y}, {w}, {h}) 样式: {style_info}")

                # 获取像素级精确匹配的字体和颜色
                if isinstance(result, dict) and 'style' in result:
                    font, font_color = self.get_precise_font_match(translated_text, style_info, language_code, bbox)
                else:
                    font, font_color = self.fit_text_to_region(translated_text, w, h, language_code, False)

                # 计算精确的文字位置 - 像素级对齐
                text_width, text_height = self.calculate_text_size(translated_text, font)

                # 精确的位置计算，确保与原文字完全重叠
                if text_level == 'title' or style_info.get('is_bold', False):
                    # 标题和加粗文字：居中对齐
                    text_x = x + (w - text_width) // 2
                    text_y = y + (h - text_height) // 2
                else:
                    # 正文：左对齐，垂直居中
                    # 使用更小的边距以确保精确对齐
                    left_margin = min(3, w // 20)  # 动态左边距
                    text_x = x + left_margin
                    text_y = y + (h - text_height) // 2

                # 微调垂直位置，确保基线对齐
                baseline_offset = style_info.get('char_height', text_height) // 8
                text_y = max(0, text_y - baseline_offset)

                # 确保文字完全在图像范围内
                text_x = max(0, min(text_x, image.width - text_width))
                text_y = max(0, min(text_y, image.height - text_height))

                # 根据原文样式绘制文字
                is_bold = style_info.get('is_bold', False)
                stroke_width = style_info.get('stroke_width', 1)

                # 对于加粗文字，使用多次绘制模拟加粗效果
                if is_bold or stroke_width > 2:
                    # 绘制加粗效果：在多个位置绘制文字
                    offsets = [(0, 0), (1, 0), (0, 1), (1, 1)]
                    for dx, dy in offsets:
                        draw.text((text_x + dx, text_y + dy), translated_text, fill=font_color, font=font)

                    logger.debug(f"绘制加粗文字: {translated_text[:20]}... 位置: ({text_x}, {text_y})")
                else:
                    # 普通文字：单次绘制
                    draw.text((text_x, text_y), translated_text, fill=font_color, font=font)

                    logger.debug(f"绘制普通文字: {translated_text[:20]}... 位置: ({text_x}, {text_y})")

                logger.info(f"像素级绘制完成 {i}: '{translated_text[:20]}...' "
                           f"位置: ({text_x}, {text_y}) 尺寸: ({text_width}x{text_height}) "
                           f"颜色: {font_color} 加粗: {is_bold}")

            except Exception as e:
                logger.error(f"像素级绘制文字 {i} 时出错: {str(e)}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
                continue

        logger.info("像素级精确文字绘制完成")
        return image

    def draw_text_with_precise_style(self, image, ocr_results, translated_texts, language_code):
        """使用精确样式绘制翻译文字（兼容旧接口）"""
        return self.draw_text_with_pixel_perfect_style(image, ocr_results, translated_texts, language_code)

    def draw_translated_text(self, image, text_regions, translated_texts, language_code):
        """在指定位置绘制翻译后的文字（兼容旧接口）"""
        # 转换为新格式
        ocr_results = []
        for region in text_regions:
            x_coords = [point[0] for point in region]
            y_coords = [point[1] for point in region]
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            bbox = (int(x_min), int(y_min), int(x_max - x_min), int(y_max - y_min))

            ocr_results.append({
                'box': region,
                'bbox': bbox,
                'style': {'font_size': 16, 'is_bold': False, 'font_color': (0, 0, 0)},
                'level': 'body'
            })

        return self.draw_text_with_precise_style(image, ocr_results, translated_texts, language_code)
    
    def process_image_translation_pixel_perfect(self, image, ocr_results, translated_texts, language_code):
        """像素级精确的图文替换处理流程，确保无残留和完全样式匹配"""
        try:
            logger.info(f"开始像素级精确图文替换处理，语言: {language_code}")
            logger.info(f"处理 {len(ocr_results)} 个文字区域，{len(translated_texts)} 个翻译文本")

            # 步骤1：使用inpaint算法彻底擦除原文字，确保无残留
            logger.info("步骤1：使用inpaint算法彻底擦除原文字")
            erased_image = self.erase_text_precisely(image, ocr_results)

            # 步骤2：使用像素级精确样式绘制翻译文字
            logger.info("步骤2：像素级精确样式绘制翻译文字")
            final_image = self.draw_text_with_pixel_perfect_style(
                erased_image, ocr_results, translated_texts, language_code
            )

            logger.info("像素级精确图文替换处理完成")
            return final_image

        except Exception as e:
            logger.error(f"像素级精确图文替换处理失败: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return image  # 返回原图

    def process_image_translation_precise(self, image, ocr_results, translated_texts, language_code):
        """精确的图文替换处理流程（兼容旧接口）"""
        return self.process_image_translation_pixel_perfect(image, ocr_results, translated_texts, language_code)

    def process_image_translation(self, image, ocr_results, translated_texts, language_code):
        """完整的图文替换处理流程，优先使用像素级精确处理"""
        try:
            # 优先使用像素级精确处理
            if ocr_results and isinstance(ocr_results[0], dict):
                # 使用像素级精确处理
                logger.info("使用像素级精确处理模式")
                return self.process_image_translation_pixel_perfect(image, ocr_results, translated_texts, language_code)
            else:
                # 兼容旧格式，但仍尝试使用精确处理
                logger.info("兼容模式：转换为精确处理格式")

                # 转换为新格式以使用精确处理
                converted_results = []
                for i, result in enumerate(ocr_results):
                    try:
                        if len(result) >= 2:
                            region = result[0]
                            text = result[1]

                            # 计算边界框
                            x_coords = [point[0] for point in region]
                            y_coords = [point[1] for point in region]
                            x_min, x_max = min(x_coords), max(x_coords)
                            y_min, y_max = min(y_coords), max(y_coords)
                            bbox = (int(x_min), int(y_min), int(x_max - x_min), int(y_max - y_min))

                            # 创建基本样式信息
                            w, h = bbox[2], bbox[3]
                            font_size = max(12, min(48, h))
                            is_bold = h > 25  # 简单的加粗判断

                            converted_result = {
                                'box': region,
                                'text': text,
                                'bbox': bbox,
                                'style': {
                                    'font_size': font_size,
                                    'is_bold': is_bold,
                                    'font_color': (0, 0, 0),
                                    'density': 0.2,
                                    'stroke_width': 2 if is_bold else 1,
                                    'char_height': font_size
                                },
                                'level': 'title' if is_bold else 'body'
                            }
                            converted_results.append(converted_result)

                    except Exception as convert_error:
                        logger.error(f"转换OCR结果 {i} 时出错: {str(convert_error)}")
                        continue

                if converted_results:
                    logger.info(f"成功转换 {len(converted_results)} 个OCR结果，使用像素级精确处理")
                    return self.process_image_translation_pixel_perfect(image, converted_results, translated_texts, language_code)
                else:
                    # 最后的回退方案
                    logger.warning("转换失败，使用基本处理模式")
                    text_regions = [result[0] for result in ocr_results]
                    erased_image = self.erase_text_region(image, text_regions)
                    final_image = self.draw_translated_text(erased_image, text_regions, translated_texts, language_code)
                    return final_image

        except Exception as e:
            logger.error(f"图文替换处理失败: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return image  # 返回原图
